"use server"
import { revalidatePath } from "next/cache";
import { scheduleClass } from "../data/schedule";
import { scheduledClassSchema } from "../schemas/scheduledClassSchema";
import { parseStateError } from "../utils/state-errors";
import { ScheduledClass } from "@/components/ui/schedule/columns";

export const scheduleClassAction = async (
  _prevState: any,
  formData: FormData
) => {
  try {
    const scls = {
      clsId: formData.get("clsId"),
      startTime: formData.get("startTime"),
      endTime: formData.get("endTime"),
      availableSlots: Number(formData.get("availableSlots")),
      coachName: formData.get("coachName"),
    };
    const validatedData = scheduledClassSchema.parse(scls);
    const validatedScls: ScheduledClass = {
      cid: validatedData.clsId,
      bookedMembers: [],
      ...validatedData,
    };

    const response = await scheduleClass(validatedScls);

    // Revalidate the member's page and the members list
    revalidatePath(`/dashboard/schedule`);

    return {
      success: true,
      errors: null,
      data: response,
    };
  } catch (error) {
    console.log(error)
    return parseStateError(error as Error);
  }
};