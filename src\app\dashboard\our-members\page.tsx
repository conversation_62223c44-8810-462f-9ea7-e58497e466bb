"use client";

import MembersContainer from "@/components/ui/members/members-container";
import { Separator } from "@/components/ui/separator";
import { Users } from "lucide-react";

export default function Page() {
  return (
    <div className="flex min-h-full flex-col gap-8 p-8">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          <Users className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">Our Members</h1>
            <p className="text-sm text-muted-foreground">
              View and manage your gym members
            </p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Main Content */}
      <div className="flex-1">
        <MembersContainer />
      </div>
    </div>
  );
}
