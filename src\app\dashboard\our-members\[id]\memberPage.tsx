import { Member } from "@/components/ui/members/columns";
import MemberDetails from "@/components/ui/cards/member-details";
import Packages from "@/components/ui/cards/packages";
import Bookings from "@/components/ui/cards/bookings";
import { Package } from "@/components/ui/packages/columns";
import { ScheduledClass } from "@/components/ui/schedule/columns";

export default function MemberPage({ member, packages, scheduledClasses }: { member: Member, packages: Package[], scheduledClasses: ScheduledClass[] }) {
  return (
    <div className="p-6 space-y-6">
      <MemberDetails member={member}/>
      <div className="flex flex-row gap-4">
        <Packages 
          memberPackages={member?.packages || []} 
          uid={member?.id || ''} 
          packages={packages}
        />
        <Bookings bookings={member?.bookings || []} scheduledClasses={scheduledClasses} uid={member.id}/>
      </div>

    </div>
  );
}
