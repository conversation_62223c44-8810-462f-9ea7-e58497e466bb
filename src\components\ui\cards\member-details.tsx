import { <PERSON>, <PERSON><PERSON><PERSON>er, CardContent } from "@/components/ui/card";
import { Mail, Phone, User, Package } from "lucide-react";

export default function MemberDetails({ member }: any) {
  return (
    <Card>
      <CardHeader>
        <h2 className="text-2xl font-bold">Member Details</h2>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="flex items-center space-x-4">
              <User className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Name</p>
                <p className="font-medium">{member.name}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Phone className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Phone</p>
                <p className="font-medium">{member.phone}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Mail className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Email</p>
                <p className="font-medium">{member.email}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <Package className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Active Packages</p>
                <p className="font-medium">{member.activePkgs}</p>
              </div>
            </div>
            
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
