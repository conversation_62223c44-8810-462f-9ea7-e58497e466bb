import { ScheduledClass } from "@/components/ui/schedule/columns";
import { ClassScan } from "@/components/ui/scans/class-container";
import { ClassContainerProps } from "@/components/ui/scans/class-container";

export const parseScans = (scheduledClasses: ScheduledClass[]) => {
  let output: ClassContainerProps[] = [];
  scheduledClasses.forEach((cls) => {
    let parsedScans: ClassScan[] = [];
    cls.scans.forEach((scan: any) => {
      const parsedScan: ClassScan = {
        member: scan.uid.name,
        phone: scan.uid.phoneNumber,
        time: new Date(scan.scanTime).toLocaleTimeString(),
        status: "Success",
      };
      parsedScans.push(parsedScan);
    });
    output.push({
      classData: cls,
      classScans: parsedScans,
    });
  });
  return output;
};
