"use client";
import { useEffect, useState } from "react";
import { Calendar } from "@/components/ui/calendar";
import { ScheduledClassesContainer } from "@/components/ui/schedule/scheduled-classes-container";
import { ScheduledClass } from "@/components/ui/schedule/columns";

interface SchedulePageProps {
  fetchScheduledClasses: (date?: Date) => Promise<ScheduledClass[] | []>;
  classIdsMap: Map<string, string>;
}

export function SchedulePage({ fetchScheduledClasses, classIdsMap }: SchedulePageProps) {
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [scheduledClasses, setScheduledClasses] = useState<ScheduledClass[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchClasses = async () => {
      setIsLoading(true);
      try {
        const classes = await fetchScheduledClasses(date);
        setScheduledClasses(classes);
      } catch (error) {
        console.error('Failed to fetch scheduled classes:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchClasses();
  }, [date, fetchScheduledClasses]);

  return (
    <div className="flex h-[calc(100vh-4rem)] gap-4 p-3">
      {/* Left side - Classes */}
      <div className="flex-[2] h-full">
        <ScheduledClassesContainer
          scheduledClasses={scheduledClasses}
          classIdsMap={classIdsMap}
          isLoading={isLoading}
          date={date || new Date()}
        />
      </div>

      {/* Right side - Calendar and Members */}
      <div className="flex flex-1 min-w-[280px] max-w-[350px] flex-col gap-3">
        <div className="h-auto">
          <Calendar
            mode="single"
            selected={date}
            onSelect={setDate}
            className="rounded-md border bg-card"
            classNames={{
              months: "flex flex-col sm:flex-row space-y-1 sm:space-x-1 sm:space-y-0",
              month: "space-y-2",
              caption: "flex justify-center relative items-center h-8",
              caption_label: "text-sm font-medium",
              nav: "space-x-1 flex items-center",
              nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
              row: "flex w-full mt-1",
              cell: "text-center text-sm relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20 h-8 w-8",
              day: "h-8 w-8 p-0 font-normal",
              day_selected: "bg-primary text-primary-foreground hover:bg-primary",
              day_today: "bg-accent text-accent-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
            }}
          />
        </div>
        <div className="flex-1">
          {/* <BookedMembersContainer
            members={}
          /> */}
        </div>
      </div>
    </div>
  );
}
