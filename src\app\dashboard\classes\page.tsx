import { ClassesPage } from "./classesPage";
import { getClasses } from "@/lib/data/class";
import { Class } from "@/components/ui/classes/columns";
import { NotFoundError } from "@/core/api-error";

const Page = async () => {
  try{
    const classes: Class[] = await getClasses();
    return <ClassesPage classes={classes}/>;
  }catch(error){
    if(error instanceof NotFoundError){
      return <ClassesPage classes={[]}/>;
    }
  }
};

export default Page;
