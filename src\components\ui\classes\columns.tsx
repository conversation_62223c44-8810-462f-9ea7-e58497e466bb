import { ColumnDef } from "@tanstack/react-table";
import EditClassDialog from "../dialogs/class/edit-class";
import DeleteClassDialog from "../dialogs/class/delete-class";

export type Class = {
  _id: string;
  title: string;
  category: string;
  price: string;
  location: string;
};

export const columns: ColumnDef<Class>[] = [
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "category",
    header: "Category",
  },
  {
    accessorKey: "price",
    header: "Price",
  },
  {
    accessorKey: "location",
    header: "Location",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const cls = row.original;
      return (
        <div className="flex gap-2">
          <EditClassDialog cls={cls}/>
          <DeleteClassDialog cls={cls}/>
        </div>
      );
    },
  },
];
