"use client";
import { useEffect, useState } from "react";
import { But<PERSON> } from "../button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "../tooltip";
import { Users } from "lucide-react";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "../dialog";
import { BookedMembersContainer } from "./booked-members-container";

export type Member = {
  name: string;
  phone: string;
  uid: string;
  _id: string;
};

export function ShowBookedMembers({ members }: { members: Member[] }) {
  const [memberNames, setMemberNames] = useState<string[]>([]);
  const [open, setOpen] = useState(false);
  useEffect(() => {
    console.log(members)
    let names: string[] = [];
    members.forEach((member) => {
      names.push(member.name);
    });
    setMemberNames(names);
  }, [members, setMemberNames]);
  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              className="h-8 w-8 p-0 cursor-pointer"
              onClick={() => setOpen(true)}
            >
              <Users className="h-4 w-4" />
              <span className="sr-only">Show booked members</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>Show booked members</TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogHeader>
          <DialogTitle></DialogTitle>
        </DialogHeader>
        <DialogContent>
          <div className="pt-4">
            <BookedMembersContainer members={memberNames} />
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
