  import { getClasses } from "@/lib/data/class";
  import { SchedulePage } from "./schedulePage";
  import { getScheduledClasses } from "@/lib/data/schedule";
  import { Class } from "@/components/ui/classes/columns";
  import { NotFoundError } from "@/core/api-error";

  export default async function Page() {
    const fetchScheduledClasses = async (date?: Date) => {
      "use server";
      try {
        const scheduledClasses = await getScheduledClasses(date || new Date());
        return scheduledClasses;
      } catch (error) {
        if (error instanceof NotFoundError) {
          return [];
        }
        throw error;
      }
    };
    const getClassIds = async () => {
      try {
        const classes: Class[] = await getClasses();
        const classIdsMap = new Map(classes.map((cls) => [cls.title, cls._id]));
        return classIdsMap;
      } catch (error) {
        if (error instanceof NotFoundError) {
          return new Map();
        }
        throw error;
      }
    };
    return (
      <SchedulePage
        fetchScheduledClasses={fetchScheduledClasses}
        classIdsMap={await getClassIds()}
      />
    );
  }