"use client";

import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Search, Users } from "lucide-react";
import { Input } from "@/components/ui/input";
import { useState } from "react";

export function BookedMembersContainer({ members }: { members: string[] }) {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredMembers = members.filter((member) =>
    member.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Card className="w-full">
      <CardHeader className="">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1.5">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium">Booked Members</span>
          </div>
          <Badge variant="secondary" className="text-xs font-normal">
            {filteredMembers.length}/{members.length}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="relative">
          <Search className="absolute left-2 top-1/2 h-3.5 w-3.5 -translate-y-1/2 text-muted-foreground" />
          <Input
            placeholder="Search members..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-8 pl-7 text-xs"
          />
        </div>
        <ScrollArea className="h-[200px] pr-2">
          {filteredMembers.length === 0 ? (
            <div className="flex h-16 items-center justify-center text-xs text-muted-foreground">
              {searchQuery ? "No matching members" : "No members booked yet"}
            </div>
          ) : (
            <div className="space-y-1">
              {filteredMembers.map((member, index) => (
                <div
                  key={index}
                  className="flex items-center gap-2 rounded-md border px-2 py-1.5 text-sm transition-colors hover:bg-muted/50"
                >
                  <div className="flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-primary/10 text-xs font-medium">
                    {member.charAt(0).toUpperCase()}
                  </div>
                  <div className="flex-1 truncate">
                    <p className="truncate text-xs font-medium leading-none">
                      {member}
                    </p>
                    <p className="mt-0.5 text-[10px] text-muted-foreground">
                      #{members.indexOf(member) + 1}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
