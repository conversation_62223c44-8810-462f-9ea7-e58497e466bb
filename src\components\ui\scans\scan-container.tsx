"use client";
import { io } from "socket.io-client";
import {
  ClassContainer,
  ClassContainerProps,
  ClassScan,
} from "./class-container";
import { useEffect } from "react";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";

interface ScanError {
  code: string;
  message: string;
  time: string;
}

const socket = io(process.env.NEXT_PUBLIC_TMS_API_URL!, {
  transports: ["websocket"],
});

export function ScanContainer({
  scans,
}: {
  scans: ClassContainerProps[];
}) {
  const router = useRouter();
  useEffect(() => {
    socket.on("connect", () => {
      console.log("Connected to server");
    });

    socket.on("SUCCESS-SCAN", () => {
      console.log("SUCCESS-SCAN")
      router.push("/dashboard/scans-monitor");
    });

    socket.on("FAILED-SCAN", (payload: { code: string, member: string, message: string }) => {      
      console.log("Failed Scan")
      toast.error(`❌ ${payload.member}: ${payload.message}`, {
        duration: 5000,
        style: {
          border: "1px solid #f87171",
          padding: "12px",
          color: "#b91c1c",
        },
        iconTheme: {
          primary: "#b91c1c",
          secondary: "#ffe4e6",
        },
      });
    });

    return () => {
      socket.off("connect");
      socket.off("SUCCESS-SCAN");
      socket.off("FAILED-SCAN");
    };
  }, []);

  return (
    <div>
      <div className="flex flex-col">
        <div className="text-2xl font-bold mx-5 py-4 border-b-2">
          Upcomming Classes
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 justify-center gap-4 p-4">
          {scans.map((scan, index) => (
            <ClassContainer
              key={index}
              classData={scan.classData}
              classScans={scan.classScans}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
