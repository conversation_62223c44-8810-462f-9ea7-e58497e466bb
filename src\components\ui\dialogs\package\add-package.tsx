"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { useActionState } from "react";
import { addPackageAction } from "@/lib/actions/package-actions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { MultiSelect } from "../../multiselect";
import { Class } from "../../classes/columns";

interface ActionState {
  success: boolean;
  errors: Record<string, string> | null;
  data: any | null;
  defaultValues?: {
    _id: string;
    name: string;
    numberOfSessions: string;
    expiryPeriod: string;
    price: string;
    category: string;
    opensClasses: string[];
  };
}

const packageCategories = [
  {
    id: "functional",
    label: "Functional Training",
  },
  {
    id: "studio",
    label: "Studio",
  },
  {
    id: "openGym",
    label: "Open Gym",
  },
  {
    id: "ums",
    label: "Ultimate Mind Spacer",
  },
] as const;

export function AddPackageDialog({ classes }: { classes: Class[] }) {
  const initialState: ActionState = {
    success: false,
    errors: null,
    data: null,
    defaultValues: {
      _id: "newId",
      name: "",
      numberOfSessions: "",
      expiryPeriod: "",
      price: "",
      category: "",
      opensClasses: [],
    },
  };

  // Create a map of title to ID for easy lookup
  const classMap = new Map(classes.map((cls) => [cls.title, cls._id]));

  // Prepare options for MultiSelect - we still show titles to user
  const classesOptions = classes.map((cls) => cls.title);

  // Track both selected titles (for display) and their IDs (for form submission)
  const [selectedClassTitles, setSelectedClassTitles] = useState<string[]>([]);
  const [selectedClassIds, setSelectedClassIds] = useState<string[]>([]);

  const [state, formAction, pending] = useActionState(
    async (currentState: any, formData: FormData) => {
      const defaultValues = {
        _id: "newId",
        name: formData.get("name") as string,
        numberOfSessions: formData.get("numberOfSessions") as string,
        expiryPeriod: formData.get("expiryPeriod") as string,
        price: formData.get("price") as string,
        category: formData.get("category") as string,
        opensClasses: selectedClassIds,
      };
      const result = await addPackageAction(currentState, formData);

      if (result.success) {
        setIsOpen(false);
        return initialState;
      }
      return {
        ...result,
        defaultValues,
      };
    },
    initialState
  );

  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("");

  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>
        <Plus className="mr-2 h-4 w-4" />
        Add Package
      </Button>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[425px] z-50">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Adding a new package
            </DialogTitle>
            <DialogDescription>Edit package data</DialogDescription>
          </DialogHeader>
          <form action={formAction} className="mt-4">
            {/* Add hidden inputs for class IDs */}
            {selectedClassIds.map((classId) => (
              <input
                key={classId}
                type="hidden"
                name="opensClasses"
                value={classId}
              />
            ))}
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Package Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  defaultValue={state?.defaultValues?.name}
                />
                {state?.errors && "name" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.name}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Sessions</Label>
                  <Input
                    name="numberOfSessions"
                    type="number"
                    min={1}
                    max={100}
                    className="w-full"
                    defaultValue={state?.defaultValues?.numberOfSessions}
                  />
                  {state?.errors && "numberOfSessions" in state.errors && (
                    <p className="text-destructive text-sm">
                      {state.errors.numberOfSessions}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Expiry (days)</Label>
                  <Input
                    name="expiryPeriod"
                    type="number"
                    min={1}
                    max={365}
                    className="w-full"
                    defaultValue={state?.defaultValues?.expiryPeriod}
                  />
                  {state?.errors && "expiryPeriod" in state.errors && (
                    <p className="text-destructive text-sm">
                      {state.errors.expiryPeriod}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Price</Label>
                <Input
                  name="price"
                  type="number"
                  className="w-full"
                  defaultValue={state?.defaultValues?.price}
                />
                {state?.errors && "price" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.price}
                  </p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm font-medium">Opens Classes</Label>
                <MultiSelect
                  placeholder="Select classes"
                  selected={selectedClassTitles}
                  options={classesOptions}
                  onChange={(selected) => {
                    setSelectedClassTitles(selected);
                    // Convert selected titles to their corresponding IDs
                    const ids = selected.map(
                      (title) => classMap.get(title) || ""
                    );
                    setSelectedClassIds(ids);
                  }}
                />
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <Select
                  name="category"
                  defaultValue={
                    state?.defaultValues?.category || selectedCategory
                  }
                  disabled={pending}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {packageCategories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id}
                        className="hover:bg-accent"
                      >
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state?.errors && "category" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.category}
                  </p>
                )}
              </div>
            </div>

            <div className="flex justify-end gap-3 mt-8">
              <Button
                type="button"
                className="px-4"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="px-4"
                disabled={pending}
                variant="default"
              >
                {pending ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
