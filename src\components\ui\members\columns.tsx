import { ColumnDef } from "@tanstack/react-table";
import { Button } from "../button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import Link from "next/link";

export type MemberPackage = {
  name: string;
  pkgStartDate: string;
  pkgEndDate: string;
  status: string;
  remainingClasses: number;
  id: string;
};

export type Booking = {
  id: string;
  scid: string;
  className: string;
  bookingTime: Date;
  classTime: Date;
}

export type Member = {
  id: string;
  name: string;
  phone: string;
  email: string;
  activePkgs: number;
  packages: MemberPackage[];
  bookings: Booking[];
};

export const columns: ColumnDef<Member>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "phone",
    header: "Phone",
  },
  {
    accessorKey: "email",
    header: "Email",
  },
  {
    accessorKey: "activePkgs",
    header: "Active Packages",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const member = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Helping {member.name}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(member.phone)}
            >
              Copy phone number
            </DropdownMenuItem>
            <DropdownMenuItem>
              <Link href={`/dashboard/our-members/${member.id}`}>
                View member data
              </Link>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
