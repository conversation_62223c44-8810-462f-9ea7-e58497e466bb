"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { useActionState } from "react";
import { addClassAction } from "@/lib/actions/class-actions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus } from "lucide-react";
import { ApiError } from "@/core/api-error";

const classCategories = [
  {
    id: "functional",
    label: "Functional Training",
  },
  {
    id: "studio",
    label: "Studio",
  },
] as const;

const locations = [
  "Cairo",
  "NorthCoast"
] as const;

interface ActionState {
  success: boolean;
  errors: Record<string, string> | null | ApiError;
  data: any | null;
  defaultValues?: {
    title: string;
    category: string;
    price: string;
    location: string;
  };
}

export function AddClass() {
  const initialState: ActionState = {
    success: false,
    errors: null,
    data: null,
    defaultValues: {
      title: "",
      price: "",
      category: "",
      location: "",
    },
  };

  const [state, formAction, pending] = useActionState(
    async (currentState: any, formData: FormData) => {
      const defaultValues = {
        title: formData.get("title") as string,
        price: formData.get("price") as string,
        category: formData.get("category") as string,
        location: formData.get("location") as string,
      };
      const result = await addClassAction(currentState, formData);

      if (result.success) {
        setIsOpen(false);
        return initialState;
      }

      return {
        ...result,
        defaultValues,
      };
      },
    initialState
  );

  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");

  return (
    <div>
      <Button onClick={() => setIsOpen(true)}>
        <Plus className="mr-2 h-4 w-4" />
        Add Class
      </Button>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[425px] z-50">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Adding a new class
            </DialogTitle>
            <DialogDescription>Add class data</DialogDescription>
          </DialogHeader>
          <form action={formAction} className="mt-4">
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium">
                  Class Name
                </Label>
                <Input
                  id="title"
                  name="title"
                  type="text"
                  defaultValue={state?.defaultValues?.title}
                />
                {state?.errors && "title" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.title}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Price</Label>
                <Input
                  name="price"
                  type="number"
                  min={1}
                  className="w-full"
                  defaultValue={state?.defaultValues?.price}
                />
                {state?.errors && "price" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.price}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Location</Label>
                <Select
                  name="location"
                  defaultValue={
                    state?.defaultValues?.location || selectedLocation
                  }
                  disabled={pending}
                  onValueChange={setSelectedLocation}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {locations.map((location) => (
                      <SelectItem
                        key={location}
                        value={location}
                        className="hover:bg-accent"
                      >
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state?.errors && "category" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.category}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <Select
                  name="category"
                  defaultValue={
                    state?.defaultValues?.category || selectedCategory
                  }
                  disabled={pending}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {classCategories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id}
                        className="hover:bg-accent"
                      >
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state?.errors && "category" in state.errors && (
                  <p className="text-destructive text-sm">
                    {state.errors.category}
                  </p>
                )}
              </div>
            </div>{" "}
            {(state.errors && state.errors.message) && (
              <div className="text-destructive">{state.errors.message}</div>
            )}
            <div className="flex justify-end gap-3 mt-8">
              <Button
                type="button"
                className="px-4"
                variant="outline"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="px-4"
                disabled={pending}
                variant="default"
              >
                {pending ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
