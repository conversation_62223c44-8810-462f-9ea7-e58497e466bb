"use server";

import { tms } from "@/lib/tms-api";
import { deleteToken, setToken } from "../cookie";
export const login = async ({
  phoneNumber,
  password,
}: {
  phoneNumber: string;
  password: string;
}) => {
  try {
    const response = await tms.post("/auth/login", {
      phoneNumber,
      password,
    });
    const resData = response.data;
    await setToken(resData.data.token);
    return resData.data.user;
  } catch (error) {
    console.log(error)
    throw error
  }
};

export const logout = async () => {
  try {
    const response = await tms.get("/auth/logout");
    await deleteToken();
    return response.data.data;
  } catch (error) {
    console.log(error)
    throw error;
  }
};
