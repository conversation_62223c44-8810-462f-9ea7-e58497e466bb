"use server"
import { revalidatePath } from "next/cache";
import { deletePackage, editPackage, addPackage } from "../data/package";
import { packageSchema } from "../schemas/packageSchema";
import { parseStateError } from "../utils/state-errors";

export async function deletePackageAction(packageId: string) {
  try {
    await deletePackage(packageId);
    revalidatePath("/dashboard/packages");
    return { success: true };
  } catch (error) {
    return parseStateError(error as Error);
  }
}
export async function editPackageAction(_prevState: any, formData: FormData) {
  const pkg = {
    _id: formData.get("_id") as string,
    name: formData.get("name") as string,
    price: formData.get("price") as string,
    numberOfSessions: formData.get("numberOfSessions") as string,
    expiryPeriod: formData.get("expiryPeriod") as string,
    category: formData.get("category") as string,
    opensClasses: formData.getAll("opensClasses") as string[],
  };

  try {
    const validatedPkg = packageSchema.parse(pkg);
    await editPackage(validatedPkg);
    revalidatePath("/dashboard/packages");
    return { success: true, errors: null, data: null };
  } catch (error) {
    return parseStateError(error as Error);
  }
}

export async function addPackageAction(_prevState: any, formData: FormData) {
  const pkg = {
    _id: "newId",
    name: formData.get("name") as string,
    price: formData.get("price") as string,
    numberOfSessions: formData.get("numberOfSessions") as string,
    expiryPeriod: formData.get("expiryPeriod") as string,
    category: formData.get("category") as string,
    opensClasses: formData.getAll("opensClasses") as string[],
  };

  try {
    const validatedData = packageSchema.parse(pkg);

    await addPackage(validatedData);
    revalidatePath("/dashboard/packages");
    return { success: true, errors: null, data: null };
  } catch (error) {
    return parseStateError(error as Error);
  }
}
