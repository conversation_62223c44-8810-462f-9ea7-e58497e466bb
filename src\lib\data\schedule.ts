import { tms } from "@/lib/tms-api";
import { ScheduledClass } from "@/components/ui/schedule/columns";
import { NotFoundError } from "@/core/api-error";
import { parseSchedule } from "../utils/parsers/schedule-parser";

export const getScheduledClasses = async (
  date: Date
): Promise<ScheduledClass[]> => {
  try {
    const response = await tms.get("/admin/next-schedule");
    const scheduledClasses = parseSchedule(response.data.data);
    const output = scheduledClasses.filter((cls: ScheduledClass) => {
      const clsDate = new Date(cls.startTime).toLocaleDateString();
      return clsDate === date.toLocaleDateString();
    });
    return output;
  } catch (error) {
    if (error instanceof NotFoundError) {
      return [];
    }
    console.error(error);
    throw error;
  }
};

export const getNextScheduledClasses = async (): Promise<ScheduledClass[]> => {
  try {
    const response = await tms.get("/admin/next-schedule");
    const scheduledClasses = parseSchedule(response.data.data);
    return scheduledClasses;
  } catch (error) {
    if (error instanceof NotFoundError) {
      return [];
    }
    console.error(error);
    throw error;
  }
};

export const scheduleClass = async (scheduledClass: ScheduledClass) => {
  try {
    const response = await tms.post("/admin/schedule", scheduledClass);
    return response.data.data;
  } catch (error) {
    if (error instanceof NotFoundError) {
      return [];
    }
    console.error(error);
    throw error;
  }
};

export const cancelClass = async (scid: string) => {
  try {
    const response = await tms.post(`/admin/schedule/${scid}`);
    return response.data.data;
  } catch (error) {
    console.log(error);
  }
};
