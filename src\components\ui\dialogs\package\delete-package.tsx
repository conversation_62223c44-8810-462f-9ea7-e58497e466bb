"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useState } from "react";
import { deletePackageAction } from "@/lib/actions/package-actions";
import { Trash } from "lucide-react";

export default function DeletePackageDialog({
  pkg,
}: {
  pkg: { name: string; _id: string };
}) {
  const [open, setOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async () => {
    try {
      console.log(pkg)
      setIsDeleting(true);
      await deletePackageAction(pkg._id);
      setOpen(false);
    } catch (error) {
      console.error('Failed to delete package:', error);
    } finally {
      setIsDeleting(false);
    }
  };
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild onClick={(e) => e.stopPropagation()}>
        <div className="w-full">
          <Button
            onSelect={(e) => e.preventDefault()}
            variant="outline"
            className="cursor-pointer text-destructive hover:text-destructive`"
          >
            <Trash />
          </Button>
        </div>
      </DialogTrigger>
      <DialogContent onClick={(e) => e.stopPropagation()}>
        <DialogHeader>
          <DialogTitle>Are you sure you want to delete {pkg.name}?</DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete your
            package.
          </DialogDescription>
        </DialogHeader>
        <div className="flex justify-end gap-2 mt-4">
          <Button
            type="button"
            className="cursor-pointer"
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              setOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            type="button"
            className="cursor-pointer"
            disabled={isDeleting}
            variant="destructive"
            onClick={(e) => {
              e.stopPropagation();
              handleDelete();
            }}
          >
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
