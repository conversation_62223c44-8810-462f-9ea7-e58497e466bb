import { tms } from "@/lib/tms-api";

export const getUsers = async (
  searchString: string | undefined | null,
  page: number,
  limit: number
) => {
  try {
    const params: Record<string, string | number> = {
      page,
      limit,
    };
    if (searchString?.trim()) {
      if (/^\d+$/.test(searchString)) {
        params.phone = searchString; // Search by phone if numeric
      } else {
        params.name = searchString; // Search by name if not numeric
      }
    }
    const response = await tms.get("/admin/pending-members", {
      params,
    });
    return {
      data: response.data.data.users,
      total: response.data.data.total,
    };
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const addMember = async (uid: string) => {
  try {
    const response = await tms.post(`/admin/member/${uid}`);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
