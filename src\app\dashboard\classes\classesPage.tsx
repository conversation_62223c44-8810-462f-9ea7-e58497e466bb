"use client";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { DataTable } from "@/components/ui/classes/data-table";
import { Class, columns } from "../../../components/ui/classes/columns";
import { Dumbbell } from "lucide-react";
import { AddClass } from "@/components/ui/dialogs/class/add-class";

export function ClassesPage({ classes }: { classes: Class[] }) {

  return (
    <div className="flex min-h-full flex-col gap-8 p-8">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          <Dumbbell className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">Classes</h1>
            <p className="text-sm text-muted-foreground">
              Manage available classes
            </p>
          </div>
        </div>
        <AddClass />
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4"></div>
        </CardHeader>
        <CardContent>
          <DataTable columns={columns} data={classes} />
        </CardContent>
      </Card>

        
    </div>
  );
}
