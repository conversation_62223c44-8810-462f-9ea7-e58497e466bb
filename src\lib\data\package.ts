import { Package } from "@/components/ui/packages/columns";
import { tms } from "@/lib/tms-api";

export const getPackages = async (): Promise<Package[]> => {
  try {
    const response = await tms.get("/admin/packages");
    return response.data.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const addPackage = async (pkg: Package) => {
  try {
    const requestBody = {
      name: pkg.name,
      numberOfSessions: pkg.numberOfSessions,
      category: pkg.category,
      price: pkg.price,
      expiryPeriod: pkg.expiryPeriod,
      opensClasses: pkg.opensClasses,
    };
    const response = await tms.post("admin/packages", requestBody);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const editPackage = async (pkg: Package) => {
  try {
    const requestBody = {
      name: pkg.name,
      numberOfSessions: pkg.numberOfSessions,
      category: pkg.category,
      price: pkg.price,
      expiryPeriod: pkg.expiryPeriod,
      opensClasses: pkg.opensClasses,
    };
    const response = await tms.patch(`admin/packages/${pkg._id}`, requestBody);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export async function deletePackage(packageId: string) {
  try {
    const response = await tms.delete(`admin/packages/${packageId}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting package:", error);
    throw error;
  }
}
