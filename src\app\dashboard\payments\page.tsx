import PaymentsContainer from "@/components/ui/payments/payments-container";
import { getPayments } from "@/lib/data/payments";
import { CreditCard } from "lucide-react";
import { Separator } from "@/components/ui/separator";

export default async function Page() {
  const payments = await getPayments();
  console.log("GOT: ", payments)

  return (
    <div className="flex min-h-full flex-col gap-8 p-8">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          <CreditCard className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">Payments</h1>
            <p className="text-sm text-muted-foreground">
              Track and manage all payment transactions
            </p>
          </div>
        </div>
      </div>

      <Separator />

      {/* Main Content */}
      <div className="flex-1">
        <PaymentsContainer payments={payments} />
      </div>
    </div>
  );
}
