
"use client";

import { Payment, columns } from "./columns";
import { DataTable } from "./data-table";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "../card";
import { Input } from "../input";
import { Button } from "../button";
import { Badge } from "../badge";
import {
  Search,
  RefreshCw,
  Download,
  DollarSign,
  TrendingUp,
  Calendar,
  Users
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel
} from "../dropdown-menu";
import { useState, useMemo } from "react";
import { cn } from "@/lib/utils";

export default function PaymentsContainer({payments}: {payments: Payment[]}) {
  console.log(payments)
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Calculate payment statistics
  const stats = useMemo(() => {
    const totalAmount = payments.reduce((sum, payment) => {
      const amount = parseFloat(payment.amount);
      return sum + (isNaN(amount) ? 0 : amount);
    }, 0);

    const todayPayments = payments.filter(payment => {
      const paymentDate = new Date(payment.paymentTime);
      const today = new Date();
      return paymentDate.toDateString() === today.toDateString();
    });

    const uniqueMembers = new Set(payments.map(p => p.memberName)).size;

    const paymentMethods = payments.reduce((acc, payment) => {
      acc[payment.paymentMethod] = (acc[payment.paymentMethod] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalAmount,
      totalPayments: payments.length,
      todayPayments: todayPayments.length,
      uniqueMembers,
      paymentMethods
    };
  }, [payments]);

  // Filter payments based on search and method
  const filteredPayments = useMemo(() => {
    return payments.filter(payment => {
      const matchesSearch = searchTerm === "" ||
        payment.memberName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        payment.phone.includes(searchTerm) ||
        payment.purpose.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesMethod = selectedMethod === null || payment.paymentMethod === selectedMethod;

      return matchesSearch && matchesMethod;
    });
  }, [payments, searchTerm, selectedMethod]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    // Simulate refresh
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">${stats.totalAmount.toLocaleString()}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-muted-foreground">Total Payments</p>
                <p className="text-2xl font-bold">{stats.totalPayments}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-muted-foreground">Today's Payments</p>
                <p className="text-2xl font-bold">{stats.todayPayments}</p>
              </div>
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-1">
                <p className="text-sm font-medium text-muted-foreground">Unique Members</p>
                <p className="text-2xl font-bold">{stats.uniqueMembers}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payments Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div>
              <CardTitle>Payment Transactions</CardTitle>
              <p className="text-sm text-muted-foreground">
                {filteredPayments.length} of {payments.length} payments
              </p>
            </div>

            <div className="flex flex-col gap-2 md:flex-row md:items-center">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search payments..."
                  type="search"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 pr-4 w-full md:w-[250px]"
                />
              </div>

              {/* Payment Method Filter */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    {selectedMethod || "All Methods"}
                    {selectedMethod && (
                      <Badge variant="secondary" className="ml-2">
                        {stats.paymentMethods[selectedMethod]}
                      </Badge>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Payment Methods</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setSelectedMethod(null)}>
                    All Methods
                    <Badge variant="outline" className="ml-auto">
                      {payments.length}
                    </Badge>
                  </DropdownMenuItem>
                  {Object.entries(stats.paymentMethods).map(([method, count]) => (
                    <DropdownMenuItem
                      key={method}
                      onClick={() => setSelectedMethod(method)}
                    >
                      {method}
                      <Badge variant="outline" className="ml-auto">
                        {count}
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                >
                  <RefreshCw className={cn(
                    "mr-2 h-4 w-4",
                    isRefreshing && "animate-spin"
                  )} />
                  Refresh
                </Button>

                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <div className="rounded-md border">
            <DataTable columns={columns} data={filteredPayments} />
            {filteredPayments.length === 0 && payments.length > 0 && (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Search className="h-12 w-12 text-muted-foreground/50" />
                <h3 className="mt-4 text-lg font-semibold">No payments found</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Try adjusting your search or filters
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={() => {
                    setSearchTerm("");
                    setSelectedMethod(null);
                  }}
                >
                  Clear filters
                </Button>
              </div>
            )}
            {payments.length === 0 && (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <DollarSign className="h-12 w-12 text-muted-foreground/50" />
                <h3 className="mt-4 text-lg font-semibold">No payments yet</h3>
                <p className="mt-2 text-sm text-muted-foreground">
                  Payment transactions will appear here once they are recorded
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
