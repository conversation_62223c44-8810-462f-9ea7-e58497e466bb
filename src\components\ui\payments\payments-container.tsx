
import { Payment, columns } from "./columns";
import { DataTable } from "./data-table";
import { Card, CardHeader, CardTitle, CardContent } from "../card";

export default function PaymentsContainer({payments}: {payments: Payment[]}) {
  return (
    <Card className="w-full">
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div>
              <CardTitle>Payments</CardTitle>
              <p className="text-sm text-muted-foreground">
                  View Payments
              </p>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="mt-6 rounded-md border">
            <div className="relative">
              <DataTable columns={columns} data={payments} />
              {payments.length === 0 && (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <h3 className="mt-4 text-lg font-semibold">No Payments found</h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Try adjusting your search or filters
                  </p>
                </div>
              )}
            </div>
        </div>
      </CardContent>
    </Card>
  );
}
