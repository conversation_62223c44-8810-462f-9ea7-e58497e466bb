"use client";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Package as PackageIcon } from "lucide-react";
import { DataTable } from "@/components/ui/packages/data-table";
import { Package, columns } from "../../../components/ui/packages/columns";
import { AddPackageDialog } from "@/components/ui/dialogs/package/add-package";
import { Class } from "@/components/ui/classes/columns";

export default function PackagesPage({ packages, classes }: { packages: Package[]; classes: Class[] }) {

  return (
    <div className="flex min-h-full flex-col gap-8 p-8">
      {/* Header Section */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-3">
          <PackageIcon className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-2xl font-bold">Packages</h1>
            <p className="text-sm text-muted-foreground">
              Manage available packages
            </p>
          </div>
        </div>
        <AddPackageDialog classes={classes}/>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center gap-4"></div>
        </CardHeader>
        <CardContent>
          <DataTable columns={columns} data={packages} />
        </CardContent>
      </Card>
    </div>
  );
}
