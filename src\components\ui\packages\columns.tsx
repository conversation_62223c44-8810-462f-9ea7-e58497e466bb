import { ColumnDef } from "@tanstack/react-table";
import DeletePackageDialog from "../dialogs/package/delete-package";
import EditPackageDialog from "../dialogs/package/edit-package";

export type Package = {
  _id: string;
  name: string;
  numberOfSessions: string;
  expiryPeriod: string;
  category: string;
  price: string;
  opensClasses: string[];
};

export const columns: ColumnDef<Package>[] = [
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "numberOfSessions",
    header: "Sessions",
  },
  {
    accessorKey: "expiryPeriod",
    header: "Expiry Period",
  },
  {
    accessorKey: "category",
    header: "Category",
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const pkg = row.original;
      return (
        <div className="flex gap-2 w-full">
          <EditPackageDialog pkg={pkg} classes={[]}/>
          <DeletePackageDialog pkg={pkg} />
        </div>
      );
    },
  },
];
