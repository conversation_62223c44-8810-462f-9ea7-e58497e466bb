"use server"
import { revalidatePath } from "next/cache";
import { editMemberPackage, subscribeMemberToPackage } from "../data/member";
import { parseStateError } from "../utils/state-errors";
import { bookClass } from "../data/bookings";

export const addClassesAction = async (_prevState: any, formData: FormData) => {
  try {
    const remainingClasses = formData.get("remainingClasses");
    const pkgId = formData.get("pkgId");
    const uid = formData.get("uid");
    const response = await editMemberPackage(
      uid as string,
      pkgId as string,
      Number(remainingClasses),
      null
    );

    // Revalidate the member's page and the members list
    revalidatePath(`/dashboard/our-members/${uid}`);
    revalidatePath("/dashboard/our-members");

    return {
      success: true,
      errors: null,
      data: response,
    };
  } catch (error) {
    return parseStateError(error as Error);
  }
};

export const changePkgEndDate = async (_prevState: any, formData: FormData) => {
  try {
    const uid = formData.get("uid") as string;
    const pkgId = formData.get("pkgId") as string;
    const date = formData.get("date") as string;

    const response = await editMemberPackage(uid, pkgId, null, date);

    // Revalidate the member's page and the members list
    revalidatePath(`/dashboard/our-members/${uid}`);
    revalidatePath("/dashboard/our-members");

    return {
      success: true,
      errors: null,
      data: response,
    };
  } catch (error) {
    return parseStateError(error as Error);
  }
};

export const subscribePackageAction = async (
  _prevState: any,
  formData: FormData
) => {
  try {
    const uid = formData.get("uid") as string;
    const pkgId = formData.get("pkgId") as string;
    const startDate = formData.get("startDate") as string;
    const endDate = formData.get("endDate") as string;

    const response = await subscribeMemberToPackage(
      uid,
      pkgId,
      startDate,
      endDate
    ); // Revalidate the member's page and the members list

    return {
      success: true,
      errors: null,
      data: response,
    };
  } catch (error) {
    return parseStateError(error as Error);
  }
};

export const bookClassAction = async (_prevState: any, formData: FormData) => {
  try {
    const uid = formData.get("uid") as string;
    const clsId = formData.get("clsId") as string;
    const response = await bookClass(uid, clsId);

    revalidatePath(`/dashboard/our-members/${uid}`);
    revalidatePath("/dashboard/our-members");

    return {
      success: true,
      errors: null,
      data: response,
    };
  } catch (error) {
    return parseStateError(error as Error);
  }
};
