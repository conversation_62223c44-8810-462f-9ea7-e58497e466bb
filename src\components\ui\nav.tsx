'use client'
import { B<PERSON><PERSON><PERSON><PERSON>, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "./breadcrumb";
import { usePathname } from "next/navigation";
import { getPageTitle } from "@/lib/config/pages";
export const Nav = () => {
    const pathname = usePathname();
    console.log("HIIII")
    console.log(pathname)
  return (
    <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="#">
                      The Mind Space Dashboard
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>{getPageTitle(pathname)}</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
  );
};
