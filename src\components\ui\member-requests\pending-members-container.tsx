"use client";

import { User, columns } from "./columns";
import { DataTable } from "./data-table";
import { getUsers } from "@/lib/data/users";
import MembersPagination from "./members-pagination";
import { usePathname, useSearchParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { useDebounce } from "@/hooks/useDebounce";
import { useRouter } from "next/navigation";
import Loading from "../loading/members-table";
import { Input } from "../input";
import { Button } from "../button";
import { Card, CardContent, CardHeader, CardTitle } from "../card";
import { NotFoundError } from "@/core/api-error";
import { Search, RefreshCw, Users } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
} from "../dropdown-menu";
import { Badge } from "../badge";
import { cn } from "@/lib/utils";

export default function PendingMembersContainer() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [data, setData] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const page = Number(searchParams.get("page")) || 1;
  const debouncedTerm = useDebounce(searchTerm, 500);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await getUsers(debouncedTerm || null, page, 10);
      console.log(response)
      const users = response.data;
      const renderedUsers: User[] = [];
      for (const user in users) {
        renderedUsers.push({
          id: users[user]._id,
          name: users[user].name,
          phone: users[user].phoneNumber,
          email: users[user].email,
        });
      }
      setData(renderedUsers);
      setTotalUsers(response.total);
    } catch (error) {
      if (error instanceof NotFoundError) {
        setData([]);
        setTotalUsers(0);
      }
    }
    setIsLoading(false);
  }, [page, debouncedTerm]);

  useEffect(() => {
    fetchData();
  }, [fetchData, page]);

  useEffect(() => {
    if (debouncedTerm !== searchParams.get("searchString")) {
      const params = new URLSearchParams(searchParams.toString());
      if (!debouncedTerm) {
        params.set("searchString", debouncedTerm);
      } else {
        params.delete("searchString");
      }
      if (!params.has("page")) {
        params.set("page", "1");
      }

      router.push(`${pathname}?${params.toString()}`);
    }
  }, [debouncedTerm, router, pathname, searchParams]);

  const handlePageChange = useCallback(
    (newPage: number) => {
      if (newPage < 1) return;
      const params = new URLSearchParams(searchParams.toString());
      params.set("page", newPage.toString());
      if (debouncedTerm) params.set("searchString", debouncedTerm);
      router.push(`${pathname}?${params.toString()}`);
    },
    [router, pathname, searchParams, debouncedTerm]
  );

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchData();
    setIsRefreshing(false);
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Users className="h-6 w-6 text-muted-foreground" />
            <div>
              <CardTitle>Pending Member Requests</CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage and view all pending member requests
              </p>
            </div>
          </div>
          <Badge variant="secondary" className="font-normal">
            Total: {totalUsers}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-1 items-center gap-2 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search members..."
                type="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-9 pr-4"
              />
            </div>
            <DropdownMenu>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem>Active Members</DropdownMenuItem>
                <DropdownMenuItem>Expired Members</DropdownMenuItem>
                <DropdownMenuItem>Recent Activity</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
            >
              <RefreshCw
                className={cn("mr-2 h-4 w-4", isRefreshing && "animate-spin")}
              />
              Refresh
            </Button>
          </div>
        </div>

        <div className="mt-6 rounded-md border">
          {isLoading ? (
            <Loading />
          ) : (
            <div className="relative">
              <DataTable columns={columns} data={data} />
              {data.length === 0 && !isLoading && (
                <div className="flex flex-col items-center justify-center py-12 text-center">
                  <Users className="h-12 w-12 text-muted-foreground/50" />
                  <h3 className="mt-4 text-lg font-semibold">
                    No members found
                  </h3>
                  <p className="mt-2 text-sm text-muted-foreground">
                    Try adjusting your search or filters
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="mt-4 flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Showing {data.length} of {totalUsers} users
          </p>
          <MembersPagination
            currentPage={page}
            maxPages={Math.ceil(totalUsers / 10)}
            onPageChange={handlePageChange}
          />
        </div>
      </CardContent>
    </Card>
  );
}
