import PackagesPage from "./packagesPage";
import { getPackages } from "@/lib/data/package";
import { getClasses } from "@/lib/data/class";
import { NotFoundError } from "@/core/api-error";
import { Package } from "@/components/ui/packages/columns";
import { Class } from "@/components/ui/classes/columns";

export default async function Page() {
  let packages: Package[] = [];
  let classes: Class[] = [];
  try {
    classes = await getClasses(); 
    packages = await getPackages();
    return <PackagesPage packages={packages} classes={classes} />;
  } catch (error) {
    if (error instanceof NotFoundError) {
      return <PackagesPage packages={packages} classes={classes} />;
    }
  }
}
