"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { useState } from "react";
import { useActionState } from "react";
import { editClassAction } from "@/lib/actions/class-actions";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import z from "zod";
import { classSchema } from "@/lib/schemas/classSchema";
import { Class } from "../../classes/columns";
import { Edit } from "lucide-react";
import { ApiError, InternalError } from "@/core/api-error";

interface ActionState {
  success: boolean;
  errors: Record<string, string> | ApiError | null;
  data: any | null;
  defaultValues?: Record<string, string>;
}

const classCategories = [
  {
    id: "functional",
    label: "Functional Training",
  },
  {
    id: "studio",
    label: "Studio",
  },
] as const;

const locations = ["Cairo", "NorthCoast"] as const;

export default function EditClassDialog({ cls }: { cls: Class }) {
  const [open, setOpen] = useState(false);
  const [_selectedCategory, setSelectedCategory] = useState(cls.category);
  const [_selectedLocation, setSelectedLocation] = useState(cls.location);
  const initialState: ActionState = {
    success: false,
    errors: null,
    data: null,
    defaultValues: {
      title: cls.title,
      price: cls.price,
      category: cls.category,
      location: cls.location,
    },
  };

  const [state, formAction, pending] = useActionState(
    async (currentState: any, formData: FormData) => {
      const defaultValues = {
        title: formData.get("title") as string,
        price: formData.get("price") as string,
        category: formData.get("category") as string,
        location: formData.get("location") as string,
      };
      const result = await editClassAction(currentState, formData);
      if (result.success) {
        setOpen(false);
        return initialState;
      }

      return {
        ...result,
        defaultValues,
      };
    },
    initialState
  );

  return (
    <div>
      <Button
        onSelect={(e) => e.preventDefault()}
        onClick={() => setOpen(true)}
        variant="outline"
      >
        <Edit />
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px] z-50">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Editing {cls.title} class
            </DialogTitle>
            <DialogDescription>Edit class data</DialogDescription>
          </DialogHeader>
          <form action={formAction} className="mt-4">
            <input type="hidden" name="_id" value={cls._id} />
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium">
                  Class Name
                </Label>
                <Input
                  id="title"
                  name="title"
                  type="text"
                  defaultValue={state.defaultValues?.title}
                />
                {state.errors && "title" in state.errors && (
                  <div className="text-destructive text-sm">
                    {state.errors.title}
                  </div>
                )}
              </div>
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="price" className="text-sm font-medium">
                    Price
                  </Label>
                  <Input
                    id="price"
                    name="price"
                    type="number"
                    defaultValue={state.defaultValues?.price}
                  />
                </div>
                {state.errors && "price" in state.errors && (
                  <div className="text-destructive text-sm">
                    {state.errors.price}
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <Select
                  name="category"
                  defaultValue={state.defaultValues?.category}
                  disabled={pending}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {classCategories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id}
                        className="cursor-pointer hover:bg-accent"
                      >
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state.errors && "category" in state.errors && (
                  <div className="text-destructive text-sm">
                    {state.errors.category}
                  </div>
                )}
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Location</Label>
                <Select
                  name="location"
                  defaultValue={state.defaultValues?.location}
                  disabled={pending}
                  onValueChange={setSelectedLocation}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {locations.map((location) => (
                      <SelectItem
                        key={location}
                        value={location}
                        className="hover:bg-accent"
                      >
                        {location}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {state.errors && "location" in state.errors && (
                  <div className="text-destructive text-sm">
                    {state.errors.location}
                  </div>
                )}
              </div>

              {state.errors && state.errors.message && (
                <div className="text-destructive">{state.errors.message}</div>
              )}
            </div>

            <div className="flex justify-end gap-3 mt-8">
              <Button
                type="button"
                className="px-4"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="px-4"
                disabled={pending}
                variant="default"
              >
                {pending ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
