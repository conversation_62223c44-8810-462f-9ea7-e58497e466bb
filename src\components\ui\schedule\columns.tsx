import { ColumnDef } from "@tanstack/react-table";
import { Button } from "../button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../dropdown-menu";
import { MoreHorizontal } from "lucide-react";
import { ShowBookedMembers } from "./show-booked-members";
import { Member } from "./show-booked-members";


export type ScheduledClass = {
  _id?: string;
  cid: string;
  startTime: string;
  endTime: string;
  availableSlots: number;
  bookedMembers: Member[];
  coachName: string;
  className?: string;
  location?: string;
  scans: any;  
};

export const columns: ColumnDef<ScheduledClass>[] = [
  {
    accessorKey: "className",
    header: "Class Name",
  },
  {
    accessorKey: "availableSlots",
    header: "Available Slots",
  },
  {
    accessorKey: "startTime",
    header: "Start Time",
    cell: ({ row }) => {
      const scls = row.original;
      return new Date(scls.startTime).toLocaleTimeString();
    },
  },
  {
    accessorKey: "endTime",
    header: "End Time",
    cell: ({ row }) => {
      const scls = row.original;
      return new Date(scls.endTime).toLocaleTimeString();
    },
  },
  {
    accessorKey: "coachName",
    header: "Coach Name",
  },
  {
    accessorKey: "location",
    header: "Location",
  },
  {
    id: "showBooked",
    cell: ({row}) => {
      const scls = row.original;
      return (
        <ShowBookedMembers members={(scls.bookedMembers as Member[])} />
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const scls = row.original;
      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0 cursor-pointer">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Editing {scls.className}</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuSeparator />
    
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
