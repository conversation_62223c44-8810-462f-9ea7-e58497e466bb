import { ColumnDef } from "@tanstack/react-table";

export type Payment = {
  memberName: string,
  phone: string,
  purpose: string,
  paymentTime: Date,
  amount: string,
  paymentMethod: string,
};

export const columns: ColumnDef<Payment>[] = [
  {
    accessorKey: "memberName",
    header: "Member Name",
  },
  {
    accessorKey: "phone",
    header: "Member Phone",
  },
  {
    accessorKey: "purpose",
    header: "Payment Purpose",
  },
  {
    accessorKey: "paymentTime",
    header: "Payment Time",
  },
  {
    accessorKey: "amount",
    header: "Paid Amount",
  },
  {
    accessorKey: "paymentMethod",
    header: "Payment Method",
  }
];
