import { ColumnDef } from "@tanstack/react-table";
import { Badge } from "../badge";
import { Avatar, AvatarFallback } from "../avatar";
import { format } from "date-fns";
import {
  CreditCard,
  Banknote,
  Smartphone,
  Building2,
  Phone
} from "lucide-react";

export type Payment = {
  memberName: string,
  phone: string,
  purpose: string,
  paymentTime: Date,
  amount: string,
  paymentMethod: string,
};

const getPaymentMethodIcon = (method: string) => {
  switch (method.toLowerCase()) {
    case 'CASH':
      return <Banknote className="h-4 w-4" />;
    case 'INSTAPAY':
    case 'APP':
      return <Smartphone className="h-4 w-4" />;
    case 'VALU':
      return <Building2 className="h-4 w-4" />;
    default:
      return <CreditCard className="h-4 w-4" />;
  }
};

const getPaymentMethodColor = (method: string) => {
    switch (method.toLowerCase()) {
    case 'CASH':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'INSTAPAY':
    case 'APP':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    case 'VALU':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    default:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
  }
};

export const columns: ColumnDef<Payment>[] = [
  {
    accessorKey: "memberName",
    header: "Member",
    enableSorting: true,
    cell: ({ row }) => {
      const name = row.getValue("memberName") as string;
      const phone = row.getValue("phone") as string;
      const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();

      return (
        <div className="flex items-center gap-3 min-w-[200px]">
          <Avatar className="h-8 w-8">
            <AvatarFallback className="text-xs font-medium">
              {initials}
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span className="font-medium">{name}</span>
            <span className="text-xs text-muted-foreground flex items-center gap-1">
              <Phone className="h-3 w-3" />
              {phone}
            </span>
          </div>
        </div>
      );
    },
  },
  {accessorKey: "phone", header: "Phone"},
  {
    accessorKey: "purpose",
    header: "Purpose",
    enableSorting: true,
    cell: ({ row }) => {
      const purpose = row.getValue("purpose") as string;
      return (
        <Badge variant="outline" className="font-normal">
          {purpose}
        </Badge>
      );
    },
  },
  {
    accessorKey: "paymentTime",
    header: "Date & Time",
    enableSorting: true,
    cell: ({ row }) => {
      const date = row.getValue("paymentTime") as Date;
      return (
        <div className="flex flex-col min-w-[120px]">
          <span className="font-medium">
            {format(new Date(date), "MMM dd, yyyy")}
          </span>
          <span className="text-xs text-muted-foreground">
            {format(new Date(date), "hh:mm a")}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: "amount",
    header: "Amount",
    enableSorting: true,
    cell: ({ row }) => {
      const amount = (row.getValue("amount") as Number).toString() as string;
      // Extract numeric value for proper formatting
      const numericAmount = parseFloat(amount.replace(/[^0-9.-]+/g, ""));

      return (
        <div className="font-mono font-semibold text-green-600 dark:text-green-400 min-w-[100px]">
          ${numericAmount.toLocaleString()}
        </div>
      );
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "Method",
    enableSorting: true,
    cell: ({ row }) => {
      const method = row.getValue("paymentMethod") as string;

      return (
        <Badge
          variant="secondary"
          className={`flex items-center gap-1.5 w-fit ${getPaymentMethodColor(method)}`}
        >
          {getPaymentMethodIcon(method)}
          {method}
        </Badge>
      );
    },
  }
];
