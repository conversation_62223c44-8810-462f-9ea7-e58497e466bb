import axios from "axios";
import {getToken} from "./cookie";
import { ApiError } from "@/core/api-error";

const API_URL = process.env.NEXT_PUBLIC_TMS_API_URL as string;

export const tms = axios.create({
  baseURL: API_URL,
  withCredentials: true,  
  headers: { 'Content-Type': 'application/json'},
});

tms.interceptors.response.use(
  (response) => response,
  (error) => {
      const apiError = ApiError.handle(error);
      return Promise.reject(apiError);
  }
);
tms.interceptors.request.use(
  async (config) => {
    try {
      const token = await getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    } catch (error) {
      console.error('Failed to get token:', error);
      return config;
    }
  },
  (error) => {
    return Promise.reject(error);
  }
);