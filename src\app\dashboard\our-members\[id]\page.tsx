import { getMembers } from "@/lib/data/member";
import MemberPage from "./memberPage";
import { Card, CardContent } from "@/components/ui/card";
import { getPackages } from "@/lib/data/package";
import { getNextScheduledClasses } from "@/lib/data/schedule";
import { Package } from "@/components/ui/packages/columns";
import { ScheduledClass } from "@/components/ui/schedule/columns";

export default async function Page({ params }: { params: { id: string } }) {
  const { id } = await params;
  const packages:  Package[] = await getPackages();
  const scheduledClasses: ScheduledClass[] = await getNextScheduledClasses();
  if (!id) {
    return (
      <Card className="m-6">
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">Member not found</p>
        </CardContent>
      </Card>
    );
  }
  const data = await getMembers(null, 1, 1, id);
  const memberData = data.data[0];


  const member = {
    id: memberData.id,
    name: memberData.name,
    phone: memberData.phone,
    email: memberData.email,
    activePkgs: memberData.packages.length,
    packages: memberData.packages,
    bookings: memberData.bookings,
  };

  if (!memberData) {
    return (
      <Card className="m-6">
        <CardContent className="flex items-center justify-center h-32">
          <p className="text-muted-foreground">Member not found</p>
        </CardContent>
      </Card>
    );
  }
  return <MemberPage member={member} packages={packages} scheduledClasses={scheduledClasses} />;
}
