import { ScheduledClass } from "@/components/ui/schedule/columns";

export const parseSchedule = (schedule: any): ScheduledClass[] => {
  const scheduledClasses: ScheduledClass[] = [];
  schedule.forEach((cls: any) => {
    let parsedBookedMembers: any = [];
    cls.bookedMembers.forEach((member: any) => {
      let parsedBookedMember = {
        name: member.name,
        phone: member.phoneNumber
      }
      parsedBookedMembers.push(parsedBookedMember);
    })
    const parsedClass: ScheduledClass = {
      _id: cls._id,
      cid: cls.cid._id,
      availableSlots: cls.availableSlots,
      className: cls.cid.title,
      coachName: cls.coachId.coachName,
      location: cls.cid.locations[0].branchName,
      startTime: new Date(cls.startTime).toLocaleString(),
      endTime: new Date(cls.endTime).toLocaleString(),
      bookedMembers: parsedBookedMembers,
      scans: cls.scans
    };
    scheduledClasses.push(parsedClass);
  });
  return scheduledClasses;
};

