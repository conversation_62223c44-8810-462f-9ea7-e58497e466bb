'use server'
import { cookies } from "next/headers";

export const getToken = async (): Promise<string> => {
  return (await cookies()).get('token')?.value || '';
}

export const setToken = async (token: string) => {
  (await cookies()).set("token", token, {
    httpOnly: true,
    secure: false,
    sameSite: "lax",
    maxAge: 30 * 24 * 60 * 60 * 1000,
    path: "/",
  });
}
export const deleteToken = async () => {
  (await cookies()).delete("token");
}

