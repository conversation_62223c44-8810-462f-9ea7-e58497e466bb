import { Payment } from "@/components/ui/payments/columns"

export const parsePayments = (payments: any) => {
    let parsedPayments: Payment[] = [];
    payments.forEach((payment: any) => {
        let purpose: string = "";
        console.log(payment)
        if(payment.pkgId){
            purpose = payment.pkgId.name;
        }else if(payment.scid) {
            purpose = payment.scid.cid.title;
        }
        let parsedPayment: Payment = {
            memberName: payment.uid.name,
            phone: payment.uid.phoneNumber,
            purpose,
            paymentTime: payment.paymentTime,
            amount: payment.amount,
            paymentMethod: payment.paymentMethod
        }
        parsedPayments.push(parsedPayment)
    })
    return parsedPayments;
}