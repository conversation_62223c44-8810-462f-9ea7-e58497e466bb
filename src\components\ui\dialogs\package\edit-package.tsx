"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { useActionState } from "react";
import { editPackageAction } from "@/lib/actions/package-actions";
import type { Package } from "../../packages/columns";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MultiSelect } from "../../multiselect";
import { Edit } from "lucide-react";
import { Class } from "../../classes/columns";

interface ActionState {
  success: boolean;
  errors: Record<string, string> | null;
  data: any | null;
  defaultValues?: Record<string, string>;
}


const packageCategories = [
  {
    id: "functional",
    label: "Functional Training",
  },
  {
    id: "studio",
    label: "Studio",
  },
  {
    id: "openGym",
    label: "Open Gym",
  },
  {
    id: "ums",
    label: "Ultimate Mind Spacer",
  },
] as const;

export default function EditPackageDialog({ pkg, classes }: { pkg: Package, classes: Class[] }) {
  const [open, setOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(pkg.category);
  const [selectedClasses, setSelectedClasses] = useState<string[]>(pkg.opensClasses);
  const initialState: ActionState = {
    success: false,
    errors: null,
    data: null,
    defaultValues: {
      name: pkg.name,
      numberOfSessions: pkg.numberOfSessions,
      expiryPeriod: pkg.expiryPeriod,
      price: pkg.price,
      category: pkg.category,
    },
  };

  const [state, formAction, pending] = useActionState(
    async (currentState: any, formData: FormData) => {
      const defaultValues = {
        _id: formData.get("_id"),
        name: formData.get("name") as string,
        numberOfSessions: formData.get("numberOfSessions") as string,
        expiryPeriod: formData.get("expiryPeriod") as string,
        price: formData.get("price") as string,
        category: formData.get("category") as string, 
      };
      const result = await editPackageAction(currentState, formData);
      if (result.success) {
        setOpen(false);
        return initialState;
      } else {
        return {
          ...result,
          defaultValues,
        };
      }
    },
    initialState
  );

  return (
    <div>
      <Button
        onSelect={(e) => e.preventDefault()}
        onClick={() => setOpen(true)}
        variant="outline"
        className="cursor-pointer`"
      >
        <Edit />
      </Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="sm:max-w-[425px] z-50">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Editing {pkg.name} package
            </DialogTitle>
            <DialogDescription>Edit package data</DialogDescription>
          </DialogHeader>
          <form action={formAction} className="mt-4">
            <input type="hidden" name="_id" value={pkg._id} />
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Package Name
                </Label>
                <Input id="name" name="name" type="text" defaultValue={pkg.name}/>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Sessions</Label>
                  <Input
                    name="numberOfSessions"
                    type="number"
                    min={1}
                    max={100}
                    defaultValue={pkg.numberOfSessions}
                    className="w-full"
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">Expiry (days)</Label>
                  <Input
                    name="expiryPeriod"
                    type="number"
                    min={1}
                    max={365}
                    defaultValue={pkg.expiryPeriod}
                    className="w-full"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="price" className="text-sm font-medium">
                  Price
                </Label>
                <Input id="price" name="price" type="text" defaultValue={pkg.price}/>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Opens Classes</Label>
                <MultiSelect
                  placeholder="Select classes"
                  selected={selectedClasses}
                  options={[
                    "Functional Training",
                    "Studio",
                    "Open Gym",
                    "Ultimate Mind Spacer",
                  ]}
                  onChange={(selected) => {
                    setSelectedClasses(selected);
                  }}
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <Select
                  name="category"
                  defaultValue={selectedCategory}
                  disabled={pending}
                  onValueChange={setSelectedCategory}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {packageCategories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id}
                        className="cursor-pointer hover:bg-accent"
                      >
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {state.errors && state.errors.message && (
                <div className="text-destructive text-sm">{state.errors.message}</div>
              )}
            </div>

            <div className="flex justify-end gap-3 mt-8">
              <Button
                type="button"
                className="px-4"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="px-4"
                disabled={pending}
                variant="default"
              >
                {pending ? "Saving..." : "Save Changes"}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
