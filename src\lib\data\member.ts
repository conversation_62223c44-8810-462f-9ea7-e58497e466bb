import { tms } from "@/lib/tms-api";
import { parseMembers } from "../utils/parsers/members-parser";

export const getMembers = async (
  searchString: string | undefined | null,
  page: number,
  limit: number,
  uid?: string
) => {
  try {
    const params: Record<string, string | number> = {
      page,
      limit,
    };
    if (searchString?.trim()) {
      if (/^\d+$/.test(searchString)) {
        params.phone = searchString; // Search by phone if numeric
      } else {
        params.name = searchString; // Search by name if not numeric
      }
    }
    if(uid) params.uid = uid;
    const response = await tms.get("/admin/member", {
      params,
    });
    return {
      data: parseMembers(response.data.data.members),
      total: response.data.data.total, // Total members count from response data
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const editMemberPackage = async (uid: string, pkgId: string, remainingClasses: number | null, pkgEndDate: string | null) => {
  try {
    type RequestBody = {
      uid: string;
      pkgId: string;
      remainingClasses: number | null;
      pkgEndDate: string | null;
    };
    const requestBody: RequestBody = {
      uid,
      pkgId,
      remainingClasses,
      pkgEndDate
    };
    const response = await tms.patch("admin/member-packages/edit", requestBody);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const subscribeMemberToPackage = async (uid: string, pkgId: string, pkgStartDate: string, pkgEndDate: string) => {
  try {
    type RequestBody = {
      uid: string;
      pkgId: string;
      pkgStartDate: string;
      pkgEndDate: string;
    };
    const requestBody: RequestBody = {
      uid,
      pkgId,
      pkgStartDate,
      pkgEndDate
    };
    const response = await tms.post("admin/member-packages", requestBody);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
