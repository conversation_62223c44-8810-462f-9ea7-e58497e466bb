import { Member } from "@/components/ui/members/columns";
export const parseMembers = (members: any) => {
    let parsedMembers: Member[] = [];
    members.forEach((member: any) => {
        let parsedPackages: any = []
        member.packages.forEach((pkg: any) => {
            let parsedPackage = {
                _id: pkg._id,
                name: pkg.pkgId.name,
                pkgStartDate: pkg.pkgStartDate,
                pkgEndDate: pkg.pkgEndDate,
                remainingClasses: pkg.remainingClasses,
                status: pkg.status
            }
            parsedPackages.push(parsedPackage)
        })
        let parsedBookings: any = []
        member.bookings.forEach((booking: any) => {
            let parsedBooking = {
                scid: booking.scid._id,
                className: booking.scid.cid.title,
                bookingTime: booking.bookingTime,
                classTime: booking.scid.startTime,   
            }
            parsedBookings.push(parsedBooking)
        })
        const parsedMember: Member = {
            id: member.uid._id,
            name: member.uid.name,
            phone: member.uid.phoneNumber,
            email: member.uid.email,
            packages: parsedPackages,
            bookings: parsedBookings,
            activePkgs: member.packages.length
        }
        parsedMembers.push(parsedMember)
    })
    console.log(parsedMembers)
    return parsedMembers;
}