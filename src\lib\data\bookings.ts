import { tms } from "../tms-api";

export const bookClass = async (uid: string, clsId: string) => {
  try {
    const response = await tms.post("/admin/book", {
      uid,
      scid: clsId,
    });
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};

export const cancelBooking = async (uid: string, clsId: string) => {
  try {
    const response = await tms.post("/admin/cancel", { uid, clsId });
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
};
